# Especificação do Projeto - Task Manager para Eventos

## 📋 Visão Geral do Projeto

**Plataforma:** Flutter
**Objetivo:** Aplicativo para gerenciamento de tarefas em eventos com sistema de voluntariado
**Banco de Dados:** Firebase Firestore
**Autenticação:** Firebase Auth (Google Sign-In)

## 🎯 Conceito Principal

Sistema onde usuários podem criar eventos, gerenciar tarefas hier<PERSON> (Tasks → Microtasks) e coordenar voluntários para execução das atividades através de um sistema de tags/códigos únicos.

## 👥 Personas e Fluxos

### Gerenciador de Evento
- Cria eventos com informações detalhadas
- Define habilidades e recursos necessários
- Compartilha código/tag do evento
- Cria e organiza Tasks e Microtasks
- Atribui voluntários às microtasks
- Pode promover voluntários a gerenciadores

### Voluntário
- Ingressa em eventos via código/tag
- Define disponibilidade (dias, horários)
- Especifica habilidades e recursos próprios
- Recebe e executa microtasks atribuídas

## 🔄 Fluxo Principal

1. **Criação de Evento**
   - Usuário cria evento → torna-se gerenciador
   - Sistema gera código/tag único
   - Define: nome, descrição, localização, habilidades necessárias, recursos necessários

2. **Ingresso de Voluntários**
   - Voluntário insere código/tag
   - Preenche perfil: disponibilidade, habilidades, recursos
   - Aguarda atribuição de microtasks

3. **Gestão de Tarefas**
   - Gerenciador cria Tasks (grupos de atividades)
   - Cada Task contém múltiplas Microtasks
   - **Voluntários são atribuídos APENAS às Microtasks** (não às Tasks)
   - Sistema considera compatibilidade: habilidades + disponibilidade + recursos

## 📱 Especificação de Telas

### Design System
- **Cor Principal:** Roxo (#6B46C1)
- **Cor Secundária:** Roxo claro (#A78BFA)
- **Cor de Fundo:** Branco (#FFFFFF)
- **Cor de Texto:** Cinza escuro (#374151)
- **Cor de Sucesso:** Verde (#10B981)
- **Cor de Erro:** Vermelho (#EF4444)
- **Estilo:** Clean, minimalista, Material Design

### 1. Tela de Login
- **Componentes:**
  - Logo centralizado
  - Botão "Entrar com Google" (ícone + texto)
  - Link "Criar conta" na parte inferior
- **Estilo:** Fundo branco, elementos centralizados, botão roxo com bordas arredondadas

### 2. Tela de Cadastro
- **Componentes:**
  - Campos: Nome completo, E-mail, Senha, Confirmar senha
  - Botão "Criar conta"
  - Link "Já tenho conta"
- **Validações:** E-mail válido, senha mínima 6 caracteres, senhas iguais

### 3. Tela Home
- **Layout:**
  - AppBar com nome do usuário e foto
  - Lista de cards dos eventos vinculados
  - FAB (Floating Action Button) com opções:
    - "Criar Evento"
    - "Participar de Evento"
- **Event Card:**
  - Nome do evento
  - Papel do usuário (Gerenciador/Voluntário)
  - Número de tarefas pendentes
  - Status do evento

### 4. Tela Criar Evento
- **Formulário:**
  - Nome do evento (obrigatório)
  - Descrição (texto longo)
  - Localização (campo descritivo)
  - Habilidades necessárias (chips selecionáveis + adicionar nova)
  - Recursos necessários (chips selecionáveis + adicionar novo)
  - Botão "Criar Evento"
- **Pós-criação:** Exibe código/tag gerado

### 5. Tela Participar de Evento
- **Componentes:**
  - Campo para inserir código/tag
  - Botão "Buscar Evento"
  - Exibição dos detalhes do evento encontrado
  - Formulário de perfil do voluntário:
    - Dias disponíveis (checkboxes)
    - Horário disponível (time picker)
    - Habilidades (seleção múltipla)
    - Recursos disponíveis (seleção múltipla)
  - Botão "Confirmar Participação"

### 6. Tela Detalhes do Evento
- **Tabs de Navegação:**
  - **Evento:** Informações gerais, localização, código/tag
  - **Criar Tasks:** (apenas gerenciadores)
  - **Gerenciar Voluntários:** (apenas gerenciadores)
  - **Acompanhar Tasks:** Visualização de todas as tasks/microtasks

### 7. Tela Criar Tasks
- **Seção Criar Task:**
  - Nome da task
  - Descrição
  - Prioridade (Alta/Média/Baixa)
- **Seção Criar Microtask:**
  - Selecionar task pai
  - Nome da microtask
  - Descrição detalhada
  - Habilidades necessárias
  - Recursos necessários
  - Tempo estimado
  - Prioridade

### 8. Tela Gerenciar Voluntários
- **Lista de Voluntários:**
  - Cards com foto, nome, habilidades
  - Indicador de disponibilidade
  - Botão "Atribuir Microtask"
  - Botão "Promover a Gerenciador"
- **Atribuição de Microtasks:**
  - Lista de microtasks disponíveis (não atribuídas)
  - Filtro por compatibilidade com o voluntário selecionado
  - Confirmação de atribuição da microtask específica

### 9. Tela Acompanhar Tasks
- **Visualização Hierárquica:**
  - Tasks expandíveis (containers das microtasks)
  - Microtasks com status visual e voluntário atribuído
  - Filtros: Status, Prioridade, Responsável
  - Indicadores de progresso por Task (baseado nas microtasks concluídas)
- **Detalhes da Microtask:**
  - Responsável designado (voluntário específico)
  - Tempo estimado vs realizado
  - Status atual
  - Botões de ação (Iniciar/Concluir/Cancelar) - apenas para o voluntário atribuído

## 🗂️ Estrutura de Pastas

```
lib/
├── core/
│   ├── constants/
│   │   ├── app_colors.dart
│   │   ├── app_strings.dart
│   │   ├── app_dimensions.dart
│   │   └── app_constants.dart
│   ├── utils/
│   │   ├── validators.dart
│   │   ├── date_helpers.dart
│   │   ├── string_helpers.dart
│   │   └── permission_helpers.dart
│   ├── theme/
│   │   └── app_theme.dart
│   └── exceptions/
│       └── app_exceptions.dart
├── data/
│   ├── models/
│   │   ├── user_model.dart
│   │   ├── event_model.dart
│   │   ├── task_model.dart
│   │   ├── microtask_model.dart
│   │   ├── volunteer_profile_model.dart
│   │   └── user_microtask_model.dart
│   ├── repositories/
│   │   ├── auth_repository.dart
│   │   ├── event_repository.dart
│   │   ├── task_repository.dart
│   │   ├── microtask_repository.dart
│   │   └── user_repository.dart
│   └── services/
│       ├── firebase_service.dart
│       ├── auth_service.dart
│       ├── storage_service.dart
│       └── assignment_service.dart
├── presentation/
│   ├── controllers/ (usando GetX ou Provider)
│   │   ├── auth_controller.dart
│   │   ├── event_controller.dart
│   │   ├── task_controller.dart
│   │   └── volunteer_controller.dart
│   ├── screens/
│   │   ├── auth/
│   │   │   ├── login_screen.dart
│   │   │   └── register_screen.dart
│   │   ├── home/
│   │   │   └── home_screen.dart
│   │   ├── event/
│   │   │   ├── create_event_screen.dart
│   │   │   ├── join_event_screen.dart
│   │   │   ├── event_details_screen.dart
│   │   │   ├── create_tasks_screen.dart
│   │   │   ├── manage_volunteers_screen.dart
│   │   │   └── track_tasks_screen.dart
│   │   └── profile/
│   │       └── volunteer_profile_screen.dart
│   ├── widgets/
│   │   ├── common/
│   │   │   ├── custom_button.dart
│   │   │   ├── custom_text_field.dart
│   │   │   ├── custom_app_bar.dart
│   │   │   ├── loading_widget.dart
│   │   │   ├── error_widget.dart
│   │   │   └── confirmation_dialog.dart
│   │   ├── event/
│   │   │   ├── event_card.dart
│   │   │   ├── task_card.dart
│   │   │   └── event_info_card.dart
│   │   └── volunteer/
│   │       ├── volunteer_card.dart
│   │       └── skill_chip.dart
│   └── routes/
│       └── app_routes.dart
└── main.dart
```

## 🗄️ Estrutura do Banco de Dados (Firestore)

### Collection: users
```json
{
  "id": "user_id",
  "name": "Nome do Usuário",
  "email": "<EMAIL>",
  "photoUrl": "url_da_foto",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### Collection: events
```json
{
  "id": "event_id",
  "name": "Nome do Evento",
  "description": "Descrição do evento",
  "tag": "ABC123",
  "location": "Endereço descritivo",
  "createdBy": "user_id",
  "managers": ["user_id1", "user_id2"],
  "volunteers": ["user_id3", "user_id4"],
  "requiredSkills": ["skill1", "skill2"],
  "requiredResources": ["resource1", "resource2"],
  "status": "active|completed|cancelled",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### Collection: tasks
```json
{
  "id": "task_id",
  "eventId": "event_id",
  "title": "Título da Tarefa",
  "description": "Descrição da tarefa",
  "priority": "high|medium|low",
  "status": "pending|in_progress|completed",
  "createdBy": "user_id",
  "microtaskCount": 5,
  "completedMicrotasks": 2,
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### Collection: microtasks
```json
{
  "id": "microtask_id",
  "taskId": "task_id",
  "eventId": "event_id",
  "title": "Título da Microtarefa",
  "description": "Descrição da microtarefa",
  "assignedTo": "user_id",
  "requiredSkills": ["skill1"],
  "requiredResources": ["resource1"],
  "estimatedHours": 2,
  "actualHours": 1.5,
  "priority": "high|medium|low",
  "status": "pending|assigned|in_progress|completed|cancelled",
  "createdBy": "user_id",
  "assignedAt": "timestamp",
  "startedAt": "timestamp",
  "completedAt": "timestamp",
  "notes": "Observações do voluntário",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### Collection: volunteer_profiles
```json
{
  "id": "volunteer_profile_id",
  "userId": "user_id",
  "eventId": "event_id",
  "availableDays": ["monday", "tuesday", "wednesday"],
  "availableHours": {
    "start": "09:00",
    "end": "18:00"
  },
  "skills": ["skill1", "skill2"],
  "resources": ["resource1", "resource2"],
  "joinedAt": "timestamp"
}
```

## 🛠️ Stack Tecnológica

### Frontend
- **Flutter** (versão estável mais recente)
- **Dart** (linguagem principal)

### Gerenciamento de Estado
- **Provider** ou **GetX** (a definir)

### Backend & Serviços
- **Firebase Auth** (autenticação)
- **Firebase Firestore** (banco de dados)
- **Firebase Storage** (armazenamento de arquivos)

### Dependências Principais
```yaml
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.15.1
  firebase_auth: ^4.7.3
  cloud_firestore: ^4.8.5
  firebase_storage: ^11.2.6
  google_sign_in: ^6.1.4
  provider: ^6.0.5  # ou get: ^4.6.5
  cached_network_image: ^3.2.3
  intl: ^0.18.1
  uuid: ^3.0.7
```

## 🔧 Funcionalidades Específicas

### Sistema de Atribuição Inteligente
- Compatibilidade automática entre habilidades necessárias e disponíveis
- Verificação de disponibilidade de horários
- Sugestão de voluntários mais adequados **para microtasks específicas**
- Tasks servem apenas como agrupadores organizacionais

### Gerenciamento de Códigos/Tags
- Geração automática de códigos únicos alfanuméricos
- Validação de códigos existentes
- Expiração opcional de códigos

### Sistema de Status
- **Eventos:** active, completed, cancelled
- **Tasks:** pending, in_progress, completed
- **Microtasks:** pending, assigned, in_progress, completed, cancelled

## 🚀 Fases de Desenvolvimento

### Fase 1: Autenticação e Estrutura Base
- Sistema de login/cadastro
- Configuração Firebase
- Estrutura de pastas
- Tema e componentes básicos

### Fase 2: Gerenciamento de Eventos
- Criação de eventos
- Sistema de tags/códigos
- Ingresso de voluntários

### Fase 3: Sistema de Tarefas
- Criação de tasks e microtasks
- Atribuição manual de voluntários
- Acompanhamento de progresso

### Fase 4: Funcionalidades Avançadas
- Sistema de atribuição inteligente
- Perfis detalhados de voluntários
- Relatórios e estatísticas

## 📝 Considerações Importantes

### Exclusões da Versão 1.0
- Sistema de notificações push
- Testes automatizados
- Chat/mensagens entre usuários
- Sistema de avaliações/feedback

### Regras de Negócio
- Apenas gerenciadores podem criar tasks/microtasks
- Voluntários podem ser promovidos a gerenciadores
- **Voluntários são atribuídos exclusivamente às microtasks** (Tasks são apenas organizadores)
- Microtasks só podem ser atribuídas a voluntários com habilidades compatíveis
- Cada microtask pode ter apenas um voluntário atribuído
- O progresso da Task é calculado automaticamente baseado nas microtasks concluídas
- Eventos podem ter múltiplos gerenciadores

### Validações Principais
- Códigos de evento únicos
- Verificação de permissões por role
- Validação de compatibilidade antes da atribuição de microtasks
- Controle de status das microtasks (Tasks herdam status das microtasks)
- Verificação de disponibilidade do voluntário antes da atribuição
- Prevenção de atribuição múltipla da mesma microtask

---

**Observação:** Esta especificação serve como base para desenvolvimento. Detalhes de implementação e ajustes podem ser refinados durante o processo de desenvolvimento.